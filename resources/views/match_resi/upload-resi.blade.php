@extends('layouts.backend')
@section('content')
    <div class="bg-body-light">
        <div class="content content-full">
            <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center py-4">
                <div class="flex-grow-1">
                    <h2 class="h3 fw-bold mb-2">
                        Upload Resi
                    </h2>
                </div>
                <nav class="flex-shrink-0 mt-3 mt-sm-0 ms-sm-3" aria-label="breadcrumb">
                    <ol class="breadcrumb breadcrumb-alt">
                        <li class="breadcrumb-item">
                            <a class="link-fx" href="javascript:void(0)">Match Resi</a>
                        </li>
                        <li class="breadcrumb-item" aria-current="page">
                            Upload Resi
                        </li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="content">
        <div class="block block-rounded mt-4">
            <div class="block-header block-header-default">
                <h3 class="block-title">Upload Resi File</h3>
                <div class="block-options">
                    <a href="{{ asset('media/template_upload_match_resi.csv') }}" class="btn btn-primary btn-sm">
                        <i class="fa fa-download me-1"></i>
                        Download Template
                    </a>
                </div>
            </div>

            <div class="block-content block-content-full">
                <form method="POST" action="{{ route('match-resi.processData') }}" enctype="multipart/form-data"
                    id="uploadForm">
                    @csrf
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <label class="form-label">Tanggal</label>
                            <input type="text" class="js-flatpickr form-control" name="date"
                                placeholder="Pilih Tanggal" data-date-format="Y-m-d">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <label class="form-label">File (Excel/CSV)</label>
                            <input type="file" class="form-control" name="file" id="file" required
                                accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                            <small class="text-muted">File should contain column "no_resi"</small>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-4">
                            @php
                                $expedis = \App\Models\Expedisi::all();
                            @endphp
                            <label class="form-label">Ekspedisi</label>
                            <select type="text" class="form-select" name="expedisi_id" id="expedisi_id">
                                <option value="">-Pilh Expedisi-</option>
                                @foreach ($expedis as $exp)
                                    <option value="{{ $exp->id }}">{{ $exp->expedisi }}</option>
                                @endforeach
                            </select>
                            <small class="text-muted">Pilih ekspedisi yang sesuai dengan resi yang akan diupload</small>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <label class="form-label">Remark</label>
                            <textarea class="form-control" name="remark" id="remark" cols="30" rows="4"></textarea>
                        </div>
                    </div>


                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fa fa-upload me-1"></i> Upload & Process
                        </button>
                        <a href="{{ route('match-resi.index') }}" class="btn btn-alt-secondary ms-2">
                            <i class="fa fa-arrow-left me-1"></i> Kembali
                        </a>
                    </div>

                    <div class="mt-4" id="detailSection" style="display: none;">
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <label class="form-label">Total Resi:</label>
                                <div id="detailTotalResi" class="fw-bold"></div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Expedisi:</label>
                                <div id="expedisi"></div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Match:</label>
                                <div id="detailMatch" class="fw-bold text-success"></div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Not Match:</label>
                                <div id="detailNotMatch" class="fw-bold text-danger"></div>
                            </div>
                        </div>

                        <div class="table-responsive mt-4">
                            <table class="table table-bordered table-striped table-vcenter fs-sm" id="detailTable">
                                <thead>
                                    <tr class="text-center">
                                        <th>No. Resi</th>
                                        <th>Expedisi Serah Terima</th>
                                        <th>Status</th>
                                        <th>Waktu Discan</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@section('css_before')
    <link rel="stylesheet" href="{{ asset('js/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css') }}">
    <link rel="stylesheet" href="{{ asset('js/plugins/flatpickr/flatpickr.min.css') }}">
    <link rel="stylesheet" href="{{ asset('js/plugins/sweetalert2/sweetalert2.min.css') }}">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/css/select2.min.css" rel="stylesheet" />
@endsection

@section('js_after')
    <script src="{{ asset('js/lib/jquery.min.js') }}"></script>
    <script src="{{ asset('js/plugins/sweetalert2/sweetalert2.min.js') }}"></script>
    <!-- Page JS Plugins -->
    <script src="{{ asset('js/plugins/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('js/plugins/bootstrap-notify/bootstrap-notify.min.js') }}"></script>
    <script src="{{ asset('js/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js') }}"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>
    <script src="{{ asset('js/plugins/jquery-validation/jquery.validate.min.js') }}"></script>
    <script src="{{ asset('js/plugins/jquery-validation/additional-methods.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>


    <script>
        One.helpersOnLoad(['js-flatpickr']);

        // Handle form submission
        $('#uploadForm').on('submit', function(e) {
            e.preventDefault();

            const form = $(this);
            const submitBtn = $('#submitBtn');
            const originalBtnText = submitBtn.html();

            submitBtn.prop('disabled', true);
            submitBtn.html('<i class="fa fa-spinner fa-spin me-1"></i> Processing...');

            $.ajax({
                url: form.attr('action'),
                method: 'POST',
                data: new FormData(form[0]),
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        $.notify({
                            title: 'Success',
                            message: response.message,
                            icon: 'fa fa-check'
                        }, {
                            type: 'success'
                        });

                        const data = response.data;
                        $('#detailSection').show();

                        $('#detailFileName').text(data.file_name);
                        $('#detailTotalResi').text(data.total_resi);
                        $('#detailMatch').text(data.total_match);
                        $('#detailNotMatch').text(data.total_not_match);
                        $('#expedisi').html(
                            data.expedisi ?
                            `<span class="badge py-1 px-2 text-white" style="background: ${data.expedisi.color ?? '#6c757d'}; font-size: 13px">
                            ${data.expedisi.expedisi}
                        </span>` :
                            '-'
                        );

                        const detailTable = $('#detailTable tbody');
                        detailTable.empty();

                        if (data.details && data.details.length > 0) {
                            data.details.forEach(detail => {
                                const row = `
                                    <tr>
                                        <td>${detail.no_resi}</td>
                                        <td class="text-center">
                                            ${detail.serah_terima_detail?.expedisi?.expedisi ? `
                                                    <span class="badge py-1 px-2 text-white" style="background: ${detail.serah_terima_detail?.expedisi?.color ?? '#6c757d'}; font-size: 13px">
                                                        ${detail.serah_terima_detail?.expedisi?.expedisi ?? 'N/A'}
                                                    </span>
                                                ` : '-'}
                                        </td>
                                        <td class="text-center">
                                            ${
                                                parseInt(detail.is_match) === 1
                                                ? '<span class="fs-xs fw-semibold d-inline-block py-1 px-3 rounded-pill bg-success text-white fs-sm">Match</span>' 
                                                : '<span class="fs-xs fw-semibold d-inline-block py-1 px-3 rounded-pill bg-danger text-white fs-sm">No Match</span>'
                                            }
                                        </td>
                                        <td class="text-center">
                                            ${
                                                detail.serah_terima_detail?.created_at
                                                    ? moment(detail.serah_terima_detail.created_at).format('YYYY-MM-DD HH:mm:ss')
                                                    : 'N/A'
                                            }
                                        </td>
                                    </tr>
                                `;
                                detailTable.append(row);
                            });
                        } else {
                            detailTable.append(
                                '<tr><td colspan="4" class="text-center">No data available</td></tr>'
                                );
                        }

                        submitBtn.prop('disabled', false);
                        submitBtn.html(originalBtnText);

                        // $('#detailTable').scrollTop(0);

                        $('[name="start_date_report"]').val('');
                        $('[name="end_date_report"]').val('');
                        $('#file').val('');
                    } else {
                        $.notify({
                            title: 'Error',
                            message: response.message,
                            icon: 'fa fa-times'
                        }, {
                            type: 'danger'
                        });

                        submitBtn.prop('disabled', false);
                        submitBtn.html(originalBtnText);
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    let message = 'An error occurred';

                    if (response && response.message) {
                        message = response.message;
                    } else if (response && response.errors) {
                        message = Object.values(response.errors).join('<br>');
                    }

                    $.notify({
                        title: 'Error',
                        message: message,
                        icon: 'fa fa-times'
                    }, {
                        type: 'danger'
                    });

                    submitBtn.prop('disabled', false);
                    submitBtn.html(originalBtnText);
                }
            });
        });
    </script>
@endsection
