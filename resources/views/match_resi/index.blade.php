@extends('layouts.backend')
@section('content')
<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex flex-column flex-sm-row justify-content-sm-between align-items-sm-center py-4">
            <div class="flex-grow-1">
                <h2 class="h3 fw-bold mb-2">
                    Match Resi
                </h2>
            </div>
            <nav class="flex-shrink-0 mt-3 mt-sm-0 ms-sm-3" aria-label="breadcrumb">
                <ol class="breadcrumb breadcrumb-alt">
                    <li class="breadcrumb-item">
                        <a class="link-fx" href="javascript:void(0)">Match Resi</a>
                    </li>
                    <li class="breadcrumb-item" aria-current="page">
                        List Data
                    </li>
                </ol>
            </nav>
        </div>
    </div>
</div>

<div class="content">
    <div class="block block-rounded mt-4">
        <div class="block-header block-header-default">
            <h3 class="block-title">List Data Match Resi</h3>
            <div class="block-options">
                <a href="{{ route('match-resi.uploadResi') }}" class="btn btn-sm btn-primary">
                    <i class="fa fa-upload me-1"></i> Upload Resi
                </a>
            </div>
        </div>

        <div class="block-content block-content-full">
            <div class="container">
                <div class="d-flex align-items-center justify-content-start mb-3">
                    <div style="width: 15%" class="fw-bold">Waktu</div>
                    <div style="width: 85%">
                        <div class="d-flex align-items-center mt-3">
                            @php
                            $listFilter = [
                            [
                            'type' => 'all',
                            'name' => 'Semua',
                            ],
                            [
                            'type' => 'now',
                            'name' => 'Hari Ini',
                            ],
                            [
                            'type' => 'yesterday',
                            'name' => 'Kemarin',
                            ],
                            [
                            'type' => '30day',
                            'name' => '30 Hari',
                            ],
                            [
                            'type' => 'range',
                            'name' => 'Range Tanggal',
                            ],
                            ];
                            @endphp
                            @foreach ($listFilter as $item)
                            <span class="db-fltr fs-sm me-4 {{ $item['type'] == 'all' ? 'active-fl' : '' }}"
                                data-type="{{ $item['type'] }}"
                                onclick="handlerFilter('{{ $item['type'] }}')">{{ $item['name'] }}</span>
                            @endforeach
                            <div id="rangeTanggalFl" style="display: none">
                                <input type="text" class="js-flatpickr form-control" id="rangeTanggal"
                                    name="rangeTanggal" placeholder="Select Date Range" data-mode="range"
                                    data-date-format="Y-m-d">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="d-flex align-items-center justify-content-start mt-3">
                    <div style="width: 15%" class="fw-bold">Pencarian</div>
                    <div style="width: 85%">
                        <div class="d-flex align-items-center">
                            <div style="width: 60%">
                                <input type="text" name="valueSearch" id="valueSearch"
                                    class="form-control searchData w-100"
                                    style="border-top-left-radius:0;border-bottom-left-radius:0;"
                                    placeholder="Search keywoard data..." onkeydown="handlerSearchDataByKeyword(event)">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive mt-4">
                <table class="table table-bordered table-striped table-vcenter fs-sm" id="dataTable">
                    <thead>
                        <tr class="text-center">
                            <th style="width: 40px;">#</th>
                            <th style="width: 20%">File Name</th>
                            <th>Expedisi</th>
                            <th>Total Resi</th>
                            <th>Match</th>
                            <th>Not Match</th>
                            <th>Total Laporan</th>
                            <th>Total Serah Terima (SS)</th>
                            <th>Tanggal</th>
                            <th>Remark</th>
                            <th>Created By</th>
                            <th>Created At</th>
                            <th style="width: 10%;">Action</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Detail Modal -->
<div class="modal" id="modalDetail" tabindex="-1" role="dialog" aria-labelledby="modal-block-large" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="block block-rounded block-transparent mb-0">
                <div class="block-header block-header-default">
                    <h3 class="block-title">Detail Match Resi</h3>
                    <div class="block-options">
                        <button type="button" class="btn-block-option" data-bs-dismiss="modal" aria-label="Close">
                            <i class="fa fa-fw fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="block-content">
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <label class="form-label">Total Resi:</label>
                            <div id="detailTotalResi" class="fw-bold"></div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Expedisi:</label>
                            <div id="expedisi"></div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Match:</label>
                            <div id="detailMatch" class="fw-bold text-success" style="cursor: pointer;"></div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Not Match:</label>
                            <div id="detailNotMatch" class="fw-bold text-danger" style="cursor: pointer;"></div>
                        </div>
                    </div>

                    <div class="table-responsive mt-4">
                        <table class="table table-bordered table-striped table-vcenter fs-sm" id="detailTable">
                            <thead>
                                <tr class="text-center">
                                    <th>No. Resi</th>
                                    <th>Expedisi Serah Terima</th>
                                    <th>Status</th>
                                    <th>Waktu Discan</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
                <div class="block-content block-content-full text-end bg-body">
                    <button type="button" class="btn btn-sm btn-alt-secondary me-1" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal" id="modalDelete" tabindex="-1" role="dialog" aria-labelledby="modal-block-small" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="block block-rounded block-transparent mb-0">
                <div class="block-header block-header-default">
                    <h3 class="block-title">Delete Data?</h3>
                    <div class="block-options">
                        <button type="button" class="btn-block-option" data-bs-dismiss="modal" aria-label="Close">
                            <i class="fa fa-fw fa-times"></i>
                        </button>
                    </div>
                </div>
                <form method="post" id="deleteForm">
                    @csrf
                    @method('DELETE')
                    <div class="block-content">
                        <p>Are you sure you want to delete this match resi data?</p>
                    </div>
                    <div class="block-content block-content-full text-end bg-body">
                        <button type="button" class="btn btn-sm btn-alt-secondary me-1"
                            data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('css_before')
<!-- Page JS Plugins CSS -->
<link rel="stylesheet" href="{{ asset('js/plugins/bootstrap-datepicker/css/bootstrap-datepicker3.min.css') }}">

<link rel="stylesheet" href="{{ asset('js/plugins/flatpickr/flatpickr.min.css') }}">
<link rel="stylesheet" href="{{ asset('js/plugins/datatables-bs5/css/dataTables.bootstrap5.min.css') }}">
<link rel="stylesheet" href="{{ asset('js/plugins/datatables-buttons-bs5/css/buttons.bootstrap5.min.css') }}">
<link rel="stylesheet" href="{{ asset('js/plugins/select2/css/select2.min.css') }}">
{{-- <link rel="stylesheet" id="css-main" href="{{ asset('css/oneui.css')}}"> --}}

<style>
    .db-fltr {
        cursor: pointer;
    }

    .db-fltr:hover {
        padding: 7px;
        border-radius: 10px;
        background: #eff6ff;
        font-weight: normal;
    }

    .active-fl {
        padding: 7px;
        border-radius: 10px;
        background: #eff6ff;
        font-weight: bold;
    }

    .searchData:focus {
        border-color: #dfe3ea !important;
        box-shadow: none !important;
    }

    .parent-loading {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        position: fixed;
        top: 0;
        right: 0;
        left: 0;
        background: rgba(27, 27, 27, .541);
        backdrop-filter: blur(4px);
        z-index: 999;
    }

    .parent-loading .loading-custom {
        position: relative;
        padding-top: 10px;
        padding-bottom: 10px;
        padding-left: 20px;
        padding-right: 20px;
        border-radius: 10px;
        background: white;
        left: 50%;
        transform: translate(-50%, 0);
        z-index: 99999;
    }

    div[data-notify="container"] {
        z-index: 999999 !important;
    }

    .sticky-table {
        position: sticky;
        right: 0;
        background: white !important;
    }
</style>
@endsection


@section('js_after')
<script src="{{ asset('js/lib/jquery.min.js') }}"></script>
<script src="{{ asset('js/plugins/datatables/jquery.dataTables.min.js') }}"></script>
<script src="{{ asset('js/plugins/datatables-bs5/js/dataTables.bootstrap5.min.js') }}"></script>
<script src="{{ asset('js/plugins/flatpickr/flatpickr.min.js') }}"></script>
<script src="{{ asset('js/plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js') }}"></script>
<script src="{{ asset('js/plugins/bootstrap-notify/bootstrap-notify.min.js') }}"></script>
<script src="{{ asset('js/plugins/datatables-buttons/dataTables.buttons.min.js') }}"></script>
<script src="{{ asset('js/plugins/datatables-buttons/buttons.print.min.js') }}"></script>
<script src="{{ asset('js/plugins/datatables-buttons/buttons.html5.min.js') }}"></script>
<script src="{{ asset('js/plugins/datatables-buttons/buttons.flash.min.js') }}"></script>
<script src="{{ asset('js/plugins/datatables-buttons/buttons.colVis.min.js') }}"></script>
<script src="{{ asset('js/plugins/select2/js/select2.min.js') }}"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>


<script>
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    One.helpersOnLoad(['js-flatpickr']);

    let filter = {
        date_type: 'all',
        start_date: null,
        end_date: null
    };

    const table = $("#dataTable").DataTable({
        lengthMenu: [
            [10, 25, 50, 100, 500, -1],
            [10, 25, 50, 100, 500, "All"],
        ],
        searching: false,
        responsive: false,
        lengthChange: true,
        autoWidth: false,
        order: [],
        pagingType: "full_numbers",
        language: {
            search: "_INPUT_",
            searchPlaceholder: "Cari...",
            processing: '<div class="spinner-border text-info" role="status">' +
                '<span class="sr-only">Loading...</span>' +
                "</div>",
            paginate: {
                Search: '<i class="icon-search"></i>',
                first: "<i class='fas fa-angle-double-left'></i>",
                previous: "<i class='fas fa-angle-left'></i>",
                next: "<i class='fas fa-angle-right'></i>",
                last: "<i class='fas fa-angle-double-right'></i>",
            },
        },
        oLanguage: {
            sSearch: "",
        },
        processing: true,
        serverSide: true,
        ajax: {
            url: "{{ route('match-resi.dataTable') }}",
            method: "POST",
            data: function(d) {
                d.filter = filter;
                d.search = $('#valueSearch').val();
                return d;
            },
        },
        columns: [{
                data: "DT_RowIndex",
                name: "DT_RowIndex",
                orderable: false
            },
            {
                data: "file_name",
                name: "file_name",
                className: "text-nowrap",
            },
            {
                data: "expedisi",
                name: "expedisi",
                className: "text-center",
            },
            {
                data: "total_resi",
                name: "total_resi",
                className: "text-center"
            },
            {
                // data: "total_match",
                name: "total_match",
                className: "text-center text-success",
                data: function(data) {
                    return `<span class="fs-xs fw-semibold d-inline-block py-1 px-3 rounded-pill bg-success text-white fs-sm match-detail" data-id="${data.id}" style="cursor: pointer;">${data.total_match}</span>`;
                }
            },
            {
                // data: "total_not_match",
                name: "total_not_match",
                className: "text-center text-danger",
                data: function(data) {
                    return `<span class="fs-xs fw-semibold d-inline-block py-1 px-3 rounded-pill bg-danger text-white fs-sm not-match-detail" data-id="${data.id}" style="cursor: pointer;">${data.total_not_match}</span>`;
                }
            },
            {
                data: "total_laporan",
                name: "total_laporan",
                className: "text-center",
            },
            {
                data: "total_serah_terima",
                name: "total_serah_terima",
                className: "text-center",
            },
            {
                data: null,
                name: "date_range",
                className: "text-nowrap",
                render: function(data) {
                    if (data.date) {
                        return `${moment(data.date).format('DD-MM-YYYY')}`;
                    }
                    return '-';
                }
            },
             {
                data: 'remark',
                name: "remark",
                className: "text-nowrap",
            },
            {
                data: "created_by.name",
                name: "created_by.name"
            },
            {
                data: "created_at",
                name: "created_at",
                render: function(data) {
                    return moment(data).format('DD-MM-YYYY HH:mm');
                }
            },
            {
                name: "action",
                className: "sticky-table",
                data: 'action',
                orderable: false,
            },
        ],
    });

    function handlerFilter(type) {
        $('.db-fltr').removeClass('active-fl');
        $(`.db-fltr[data-type="${type}"]`).addClass('active-fl');

        filter.date_type = type;

        if (type === 'range') {
            $('#rangeTanggalFl').show();
        } else {
            $('#rangeTanggalFl').hide();
            filter.start_date = null;
            filter.end_date = null;
            table.ajax.reload();
        }
    }

    $('#rangeTanggal').on('change', function() {
        const dates = $(this).val().split(' to ');
        filter.start_date = dates[0];
        filter.end_date = dates[1] || dates[0];
        table.ajax.reload();
    });

    var debounceTimer;

    function handlerSearchDataByKeyword(e) {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => {
            filter.keyword = e.target.value;
            table.ajax.reload();
        }, 500);
    }

    let filterDetail = {}

    $(document).on('click', '.detailData', function() {
        const id = $(this).data('id');
        filterDetail = {};
        getDetail(id)
    }).on('click', '.match-detail', function() {
        const id = $(this).data('id');
        filterDetail = {
            is_match: 1,
        };
        getDetail(id);
    }).on('click', '.not-match-detail', function() {
        const id = $(this).data('id');
        filterDetail = {
            is_not_match: 1,
        };
        getDetail(id);
    }).on('click', '#detailNotMatch', function() {
        $('.detail-not-match').get(0)?.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });
    }).on('click', '#detailMatch', function() {
        $('.detail-match').get(0)?.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });
    });

    function getDetail(id) {
        $.ajax({
            url: `{{ url('match-resi') }}/${id}`,
            data: filterDetail,
            method: 'GET',
            success: function(response) {
                if (response.success) {
                    const data = response.data;

                    // Set modal content
                    $('#detailFileName').text(data.file_name);
                    $('#detailCreatedBy').text(data.created_by.name);
                    $('#detailTotalResi').text(data.total_resi);
                    $('#detailMatch').text(data.total_match);
                    $('#detailNotMatch').text(data.total_not_match);
                    $('#detailDateRange').text(
                        data.start_date_report && data.end_date_report ?
                        `${data.start_date_report} - ${data.end_date_report}` :
                        '-'
                    );
                    $('#expedisi').html(
                        data.expedisi ?
                        `<span class="badge py-1 px-2 text-white" style="background: ${data.expedisi.color ?? '#6c757d'}; font-size: 13px">
                            ${data.expedisi.expedisi}
                        </span>` :
                        '-'
                    );
                    $('#detailCreatedAt').text(moment(data.created_at).format('DD-MM-YYYY HH:mm'));
                    $('#detailDownloadBtn').attr('href', data.file_path);

                    // Populate detail table
                    const detailTable = $('#detailTable tbody');
                    detailTable.empty();

                    if (data.details && data.details.length > 0) {
                        data.details.forEach(detail => {
                            const row = `
                                    <tr class="${parseInt(detail.is_match) === 1 ? 'table-success' : 'table-danger'} detail-${parseInt(detail.is_match) === 1 ? '' : 'not-'}match" >
                                        <td>${detail.no_resi}</td>
                                        <td class="text-center">
                                            ${detail.serah_terima_detail?.expedisi?.expedisi ? `
                                                <span class="badge py-1 px-2 text-white" style="background: ${detail.serah_terima_detail?.expedisi?.color ?? '#6c757d'}; font-size: 13px">
                                                    ${detail.serah_terima_detail?.expedisi?.expedisi ?? 'N/A'}
                                                </span>
                                            ` : '-'}
                                        </td>
                                        <td class="text-center">
                                            ${
                                                parseInt(detail.is_match) === 1
                                                ? '<span class="fs-xs fw-semibold d-inline-block py-1 px-3 rounded-pill bg-success text-white fs-sm">Match</span>' 
                                                : '<span class="fs-xs fw-semibold d-inline-block py-1 px-3 rounded-pill bg-danger text-white fs-sm">No Match</span>'
                                            }
                                        </td>
                                           <td class="text-center">
                                            ${
                                                detail.serah_terima_detail?.created_at
                                                    ? moment(detail.serah_terima_detail.created_at).format('YYYY-MM-DD HH:mm:ss')
                                                    : 'N/A'
                                            }
                                        </td>
                                    </tr>
                                `;
                            detailTable.append(row);
                        });
                    } else {
                        detailTable.append('<tr><td colspan="4" class="text-center">No data available</td></tr>');
                    }

                    $('#modalDetail').modal('show');
                }
            }
        });
    }

    // Handle delete button click
    $(document).on('click', '.btn-delete', function() {
        const id = $(this).data('id');
        $('#deleteForm').attr('action', `{{ url('match-resi') }}/${id}`);
        $('#modalDelete').modal('show');
    });

    // Handle delete form submission
    $('#deleteForm').on('submit', function(e) {
        e.preventDefault();

        const form = $(this);
        const url = form.attr('action');

        $.ajax({
            url: url,
            method: 'POST',
            data: form.serialize(),
            success: function(response) {
                if (response.success) {
                    $('#modalDelete').modal('hide');
                    table.ajax.reload();

                    $.notify({
                        title: 'Success',
                        message: response.message,
                        icon: 'fa fa-check'
                    }, {
                        type: 'success'
                    });
                } else {
                    $.notify({
                        title: 'Error',
                        message: response.message,
                        icon: 'fa fa-times'
                    }, {
                        type: 'danger'
                    });
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                $.notify({
                    title: 'Error',
                    message: response.message || 'An error occurred',
                    icon: 'fa fa-times'
                }, {
                    type: 'danger'
                });
            }
        });
    });
</script>
@endsection