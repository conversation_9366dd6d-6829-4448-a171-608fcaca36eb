<?php

namespace App\Http\Controllers;

use Auth;
use App\Models\MatchResi;
use App\Models\SerahTerima;
use Illuminate\Http\Request;
use App\Models\MatchResiDetail;
use App\Imports\MatchResiImport;
use Ya<PERSON>ra\DataTables\DataTables;
use App\Models\SerahTerimaDetail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Storage;

class MatchResiController extends Controller
{
    public function index()
    {
        return view('match_resi.index');
    }

    public function uploadResi()
    {
        return view('match_resi.upload-resi');
    }

    public function processData(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimetypes:text/csv,application/csv,application/vnd.ms-excel,text/plain,csv,excel,xlsx,xls,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'date' => 'required|date',
            'expedisi_id' => 'required',
        ]);

        DB::beginTransaction();

        try {
            $import = new MatchResiImport();
            Excel::import($import, $request->file('file'));
            $dataImport = $import->rows;
            $dataImport = $dataImport->toArray();

            if (count($dataImport) === 0 || !isset($dataImport[0]['no_resi'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'File is empty or invalid format. Please make sure it contains "no_resi" column.'
                ], 422);
            }

            $resiNumbers = array_unique(array_filter(array_column($dataImport, 'no_resi')));

            if (empty($resiNumbers)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No resi numbers found in the file.'
                ], 422);
            }

            $matchResi = MatchResi::create([
                'file_name' => $request->file('file')->getClientOriginalName(),
                'total_resi' => count($resiNumbers),
                'created_by_id' => auth()->id(),
                'date' => $request->date,
                'expedisi_id' => $request->expedisi_id,
                'remark' => $request->remark
            ]);

            $detailMap = SerahTerimaDetail::whereIn(DB::raw('LOWER(no_resi)'), array_map('strtolower', $resiNumbers))
                ->get()
                ->keyBy(function ($item) {
                    return strtolower($item->no_resi);
                });

            $totalMatch = 0;
            $totalNotMatch = 0;
            $details = [];

            foreach ($dataImport as $row) {
                $noResi = $row['no_resi'] ?? null;
                if (!$noResi) continue;

                $detail = $detailMap[strtolower($noResi)] ?? null;
                $isMatch = $detail !== null;


                $details[] = [
                    'match_resi_id' => $matchResi->id,
                    'serah_terima_detail_id' => $detail->id ?? null,
                    'serah_terima_id' => $detail->serah_terima_id ?? null,
                    'expedisi_id' => $detail->expedisi_id ?? null,
                    'no_resi' => $noResi,
                    'is_match' => $isMatch,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                $isMatch ? $totalMatch++ : $totalNotMatch++;
            }

            MatchResiDetail::insert($details);

            $file_path = upload_file($request->file('file'), 'match_resi', 'match_resi_' . $matchResi->id);

            $totalPaket = SerahTerima::select('id')->with(['SerahTerimaDetails' => function ($query) {
                $query->select('id', 'no_resi')->where('deleted_at', 0);
            }])
                ->withCount(['SerahTerimaDetails as no_resi_count' => function ($query) {
                    $query->select(DB::raw('count(distinct no_resi)'))
                        ->where('deleted_at', 0);
                }])
                ->where('expedisi_id', $request->expedisi_id)
                ->whereDate('created_at', $request->date)
                ->get()->sum('no_resi_count');

            $matchResi->update([
                'total_match' => $totalMatch,
                'total_not_match' => $totalNotMatch,
                'file_path' => $file_path['file_path'],
                'total_serah_terima' => $totalPaket,
            ]);

            DB::commit();

            $matchResi->load(['details' => function ($query) {
                $query->with('serahTerimaDetail', 'serahTerimaDetail.expedisi');
            }, 'createdBy', 'expedisi']);

            return response()->json([
                'success' => true,
                'message' => 'Resi matching completed successfully!',
                'match_resi_id' => $matchResi->id,
                'data' => $matchResi,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            report($e);

            return response()->json([
                'success' => false,
                'message' => 'Failed to process file: ' . $e->getMessage()
            ], 500);
        }
    }

    public function show(Request $request, $id)
    {
        $matchResi = MatchResi::with(['createdBy', 'details' => function ($query) use ($request) {
            $query->with('serahTerimaDetail', 'serahTerimaDetail.expedisi')
                ->when($request->is_match ?? false, function ($q) {
                    $q->where('is_match', true);
                })
                ->when($request->is_not_match ?? false, function ($q) {
                    $q->where('is_match', false);
                })
                ->orderBy('is_match', 'asc');
        }, 'expedisi'])
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $matchResi,
            'details' => $matchResi->details
        ]);
    }

    public function destroy(Request $request, $id)
    {

        DB::beginTransaction();

        try {
            $matchResi = MatchResi::findOrFail($id);

            MatchResiDetail::where('match_resi_id', $id)->delete();

            if ($matchResi->file_path && File::delete($matchResi->file_path)) {
                File::delete($matchResi->file_path);
            }

            $matchResi->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Match resi data deleted successfully!'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            report($e);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete: ' . $e->getMessage()
            ], 500);
        }
    }

    public function dataTable(Request $request)
    {
        $data = MatchResi::select([
            'match_resis.id',
            'match_resis.file_name',
            'match_resis.total_resi',
            'match_resis.total_match',
            'match_resis.total_not_match',
            'match_resis.date',
            'match_resis.created_at',
            'match_resis.file_path',
            'match_resis.expedisi_id',
            'match_resis.total_serah_terima',
            'created_by.id as created_by_id',
            'created_by.name as created_by_name',
            'match_resis.remark',
        ])
            ->join('users as created_by', 'created_by.id', '=', 'match_resis.created_by_id')
            ->with(['createdBy', 'expedisi']);

        if (empty($request->order[0]['column'])) {
            $data = $data->latest();
        }
        $data = $data->filter($request->filter);

        return DataTables::of($data)
            ->addIndexColumn()
            ->addColumn('file_name', function ($row) {
                return "<a href='" . asset($row->file_path) . "' target='_blank' class='text-decoration-none'>" . $row->file_name . "</a>";
            })
            ->addColumn('expedisi', function ($row) {
                return $row->expedisi ? "<span class='badge py-1 px-2 text-white' style='background: {$row->expedisi->color}; font-size: 13px'>{$row->expedisi->expedisi}</span>" : '-';
            })
            ->addColumn('action', function ($row) {
                // $actionBtn = '<div class="d-flex align-items-center gap-2">
                //             <button data-id="' . $row->id . '" class="btn btn-sm btn-alt-secondary js-bs-tooltip-enabled detailData">
                //                 <i class="fa fa-fw fa-eye"></i>
                //             </button>
                //              <button data-id="' . $row->id . '" class="btn btn-sm btn-alt-secondary js-bs-tooltip-enabled btn-delete">
                //                <i class="fa fa-trash"></i>
                //             </button>
                // </div>';
                $actionBtn = '
                <div class="d-flex align-items-center gap-2">
                    <button data-id="' . $row->id . '" class="btn btn-sm btn-alt-secondary js-bs-tooltip-enabled detailData">
                        <i class="fa fa-fw fa-eye"></i>
                    </button>
                    <div class="dropdown">
                        <button
                            class="js-bs-tooltip-enabled btn btn-sm btn-alt-secondary "
                            type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <a href="javascript:void(0)" data-id="' . $row->id . '"
                                class="dropdown-item js-bs-tooltip-enabled btn-delete"
                                data-bs-toggle="tooltip" title="Delete"><i
                                    class="fa fa-fw fa-trash-alt"></i>
                                Hapus
                            </a>
                        </ul>
                    </div>
                </div>
                ';

                return $actionBtn;
            })
            ->addColumn('total_serah_terima', function ($row) {
                return ' <span class="fs-xs fw-semibold d-inline-block py-1 px-3 rounded-pill bg-info-light text-info fs-sm">' . $row->total_serah_terima . '</span>';
            })
            ->addColumn('total_laporan', function ($row) {
                $row->total_laporan = $row->total_match + $row->total_not_match;
                return '<span class="fs-xs fw-semibold d-inline-block py-1 px-3 rounded-pill bg-info-light text-info fs-sm">' . $row->total_laporan . '</span>';
            })
            ->rawColumns(['action', 'file_name', 'expedisi', 'total_serah_terima', 'total_laporan'])
            ->make(true);
    }
}
