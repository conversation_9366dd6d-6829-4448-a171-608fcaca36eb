<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ShopController;
use App\Http\Controllers\TaskController;
use App\Http\Controllers\ReturController;
use App\Http\Controllers\KatalogController;
use App\Http\Controllers\MatchResiController;
use App\Http\Controllers\PicReportController;
use App\Http\Controllers\CustomMenuController;
use App\Http\Controllers\KartuStockController;
use App\Http\Controllers\TextEditorController;
use App\Http\Controllers\SerahTerimaController;
use App\Http\Controllers\InboundReturController;
use App\Http\Controllers\BarangBelanjaController;
use App\Http\Controllers\RequestRefundController;
use App\Http\Controllers\LaporanBelanjaController;
use App\Http\Controllers\ManageDatabaseController;
use App\Http\Controllers\RolePermissionController;
use App\Http\Controllers\LaporanKaryawanController;
use App\Http\Controllers\ManualComplaintController;
use App\Http\Controllers\MetodePembayaranController;
use App\Http\Controllers\StockOpnameRequestController;
use App\Http\Controllers\KartuStockMenuDetailController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Example Routes
Route::get('/', function () {
    return redirect()->route('login');
});
Route::match(['get', 'post'], '/dashboard', function () {
    return view('dashboard');
});

Route::post('/getDataDashboard', [App\Http\Controllers\HomeController::class, 'getData'])->name('getData');

Route::resource('user', '\App\Http\Controllers\UserController');
Route::resource('expedisi', '\App\Http\Controllers\ExpedisiController');
Route::resource('serahterima', '\App\Http\Controllers\SerahTerimaController');

Route::get('/serah-terima/total-paket', [SerahTerimaController::class, 'getTotalPaket'])->name('serahterima.getTotalPaket');
Route::post('/scanBarcode', [App\Http\Controllers\SerahTerimaController::class, 'scanBarcode'])->name('scanBarcode');
Route::get('/listTemp', [App\Http\Controllers\SerahTerimaController::class, 'listTemp'])->name('listTemp');
Route::delete('/destroyTemp/{id}', [App\Http\Controllers\SerahTerimaController::class, 'destroyTemp'])->name('destroyTemp');
Route::delete('/deleteTempAll', [App\Http\Controllers\SerahTerimaController::class, 'deleteTempAll'])->name('deleteTempAll');
Route::delete('/destroyResi/{id}', [App\Http\Controllers\SerahTerimaController::class, 'destroyResi'])->name('destroyResi');

Route::get('/countItem', [App\Http\Controllers\SerahTerimaController::class, 'countItem'])->name('countItem');
Route::get('/printTandaTerima/{id}', [App\Http\Controllers\SerahTerimaController::class, 'printTandaTerima'])->name('printTandaTerima');
Route::get('/printTandaTerimaTerm/{id}', [App\Http\Controllers\SerahTerimaController::class, 'printTandaTerimaTerm'])->name('printTandaTerimaTerm');
Route::post('/getDataSerahTerima', [App\Http\Controllers\SerahTerimaController::class, 'getDataSerahTerima'])->name('getDataSerahTerima');
Route::get('/listFilterBulan', [App\Http\Controllers\SerahTerimaController::class, 'listFilterBulan'])->name('listFilterBulan');
Route::get('/cariResi', [App\Http\Controllers\SerahTerimaController::class, 'cariResi'])->name('cariResi');
Route::post('/updateCatatan', [App\Http\Controllers\SerahTerimaController::class, 'updateCatatan'])->name('updateCatatan');
Route::get('/getDetailSerahTerimaById/{id}', [App\Http\Controllers\SerahTerimaController::class, 'getDetailSerahTerimaById'])->name('getDetailSerahTerimaById');
Route::post('/updateDetail', [App\Http\Controllers\SerahTerimaController::class, 'updateDetail'])->name('updateDetail');

Route::get('/export', [App\Http\Controllers\SerahTerimaController::class, 'export'])->name('serahterima.exportExcel');
Route::get('/exportById/{id}', [App\Http\Controllers\SerahTerimaController::class, 'exportById'])->name('exportById');
Route::get('/exportByBulan', [App\Http\Controllers\SerahTerimaController::class, 'exportByBulan'])->name('exportByBulan');

Route::get('/blacklist', [App\Http\Controllers\BlacklistController::class, 'index'])->name('blacklist');
Route::get('/blacklist/create', [App\Http\Controllers\BlacklistController::class, 'create'])->name('blacklist.create');
Route::post('/blacklist/store', [App\Http\Controllers\BlacklistController::class, 'store'])->name('blacklist.store');
Route::get('/blacklist/{id}/show', [App\Http\Controllers\BlacklistController::class, 'show'])->name('blacklist.show');
//Route::delete('/blacklist/{id}/destroy', [App\Http\Controllers\BlacklistController::class, 'destroy'])->name('blacklist.destroy');
Route::delete('/blacklist/destroy/{id}', [App\Http\Controllers\BlacklistController::class, 'destroy'])->name('blacklist.destroy');

Route::post('/blacklist/getDataBlacklist', [App\Http\Controllers\BlacklistController::class, 'getDataBlacklist'])->name('blacklist.getDataBlacklist');
Route::post('/blacklist/scanBarcode', [App\Http\Controllers\BlacklistController::class, 'scanBarcode'])->name('blacklist.scanBarcode');
Route::delete('/blacklist/destroyResi/{id}', [App\Http\Controllers\BlacklistController::class, 'destroyResi'])->name('destroyResi');

Route::get('/log-activitas', [App\Http\Controllers\LogActivitasController::class, 'index'])->name('logactivitas');
Route::post('/log-activitas/detail', [App\Http\Controllers\LogActivitasController::class, 'detail'])->name('logactivitasdetail');

// Route::get('/setting-akses', [App\Http\Controllers\SettingAksesController::class, 'index'])->name('settingakses');
// Route::post('/setting-akses/update-akses', [App\Http\Controllers\SettingAksesController::class, 'update'])->name('updateakses');

// Route::group(['prefix' => 'retur', 'as' => 'retur.', 'controller' => ReturController::class], function () {
//     Route::post('/dataTable', 'dataTable')->name('dataTable');
// });
Route::resource('retur', ReturController::class);
Route::post('retur/dataTable', [ReturController::class, 'dataTable'])->name('retur.dataTable');
Route::get('retur/export/excel', [ReturController::class, 'export'])->name('retur.exportExcel');
Route::resource('manual-complaint', ManualComplaintController::class);
Route::post('manual-complaint/dataTable', [ManualComplaintController::class, 'dataTable'])->name('manual-complaint.dataTable');
Route::get('manual-complaint/export/excel', [ManualComplaintController::class, 'export'])->name('manual-complaint.exportExcel');
Route::resource('laporan-belanja', LaporanBelanjaController::class);
Route::post('laporan-belanja/dataTable', [LaporanBelanjaController::class, 'dataTable'])->name('laporan-belanja.dataTable');
Route::get('laporan-belanja/export/excel', [LaporanBelanjaController::class, 'export'])->name('laporan-belanja.exportExcel');
Route::post('laporan-belanja/lockUnlock/{id}', [LaporanBelanjaController::class, 'lockUnlockData'])->name('laporan-belanja.lockUnlock');
Route::resource('stock-opname-request', StockOpnameRequestController::class);
Route::post('stock-opname-request/dataTable', [StockOpnameRequestController::class, 'dataTable'])->name('stock-opname-request.dataTable');
Route::get('stock-opname-request/export/excel', [StockOpnameRequestController::class, 'export'])->name('stock-opname-request.exportExcel');
Route::resource('request-refund', RequestRefundController::class);
Route::get('request-refund/export/excel', [RequestRefundController::class, 'export'])->name('request-refund.exportExcel');
Route::post('request-refund/dataTable', [RequestRefundController::class, 'dataTable'])->name('request-refund.dataTable');
Route::resource('shop', ShopController::class);
Route::resource('pic-report', PicReportController::class);
Route::resource('barang-belanja', BarangBelanjaController::class);
Route::resource('metode-pembayaran', MetodePembayaranController::class);
Route::resource('task', TaskController::class);
Route::resource('role-permission', RolePermissionController::class);
Route::resource('inbound-retur', InboundReturController::class);
Route::post('/inbound-retur/getData', [InboundReturController::class, 'getData'])->name('inbound-retur.getData');
Route::post('/inbound-retur/scanBarcode', [InboundReturController::class, 'scanBarcode'])->name('inbound-retur.scanBarcode');
Route::get('/inbound-retur/export/excel', [InboundReturController::class, 'export'])->name('inbound-retur.exportExcel');

Route::group(['prefix' => 'custom-menu', 'as' => 'custom-menu.'],function () {
    Route::get('/', [CustomMenuController::class, 'index'])->name('index');
    Route::get('/create', [CustomMenuController::class, 'create'])->name('create');
    Route::post('/', [CustomMenuController::class, 'store'])->name('store');
    Route::get('/{id}/edit', [CustomMenuController::class, 'edit'])->name('edit');
    Route::get('/menu/{slug}', [CustomMenuController::class, 'menu'])->name('menu');
    Route::get('/menu/{slug}/create', [CustomMenuController::class, 'menuCreate'])->name('menu.create');
    Route::get('/menu/{slug}/edit/{id}', [CustomMenuController::class, 'menuEdit'])->name('menu.edit');
    Route::post('/menu/{slug}', [CustomMenuController::class, 'menuStore'])->name('menu.store');
    Route::post('/menu/{slug}/dataTable', [CustomMenuController::class, 'dataTableMenu'])->name('menu.dataTable');
    Route::post('/dataTable', [CustomMenuController::class, 'dataTable'])->name('dataTable');
    Route::delete('/{id}', [CustomMenuController::class, 'destroy'])->name('destroy');
    Route::delete('/{slug}/{id}', [CustomMenuController::class, 'menuDestroy'])->name('menu.destroy');
    Route::get('/{slug}/export-excel', [CustomMenuController::class, 'menuExportExcel'])->name('menu.exportExcel');
    Route::post('/menu/{slug}/{id}/update-editable', [CustomMenuController::class, 'menuUpdateEditable'])->name('menu.updateEditable');
    Route::post('/menu/{id}/duplicate-menu', [CustomMenuController::class, 'duplicateMenu'])->name('menu.duplicate-menu');
    Route::get('/menu/{slug}/{id}/', [CustomMenuController::class, 'showMenu'])->name('menu.show-menu');
    Route::post('/menu/{slug}/{id}/lockUnlock', [CustomMenuController::class, 'lockUnlockData'])->name('menu.lockUnlock');
});

Route::group(['prefix' => 'manage-database', 'as' => 'manage-database.', 'middleware' => 'auth'], function () {
    Route::get('/', [ManageDatabaseController::class, 'index'])->name('index');
    Route::post('/dalete-data', [ManageDatabaseController::class, 'deleteData'])->name('delete-data');
    Route::post('/restore-data', [ManageDatabaseController::class, 'restoreData'])->name('restore-data');
    Route::post('/restore-data-file', [ManageDatabaseController::class, 'restoreDataFile'])->name('restore-data-file');
    Route::post('/dataTable', [ManageDatabaseController::class, 'dataTable'])->name('dataTable');
});


Route::group(['prefix' => 'katalog'], function() {
    Route::get('/', [KatalogController::class, 'index'])->name('katalog.index');
    Route::get('/{id}', [KatalogController::class, 'show'])->name('katalog.show');
    Route::get('/detail/{id}', [KatalogController::class, 'detail'])->name('katalog.detail');
});

Route::resource('laporan-karyawan', LaporanKaryawanController::class);
Route::post('laporan-karyawan/dataTable', [LaporanKaryawanController::class, 'dataTable'])->name('laporan-karyawan.dataTable');
Route::get('laporan-karyawan/export/excel', [LaporanKaryawanController::class, 'export'])->name('laporan-karyawan.exportExcel');

Route::group(['prefix' => 'textEditor'], function() {
    Route::post('/uploadPhoto',  [TextEditorController::class, 'uploadPhoto'])->name('uploadPhoto');
    Route::post('/deletePhoto',  [TextEditorController::class, 'deletePhoto'])->name('deletePhoto');
});

Route::group(['prefix' => 'match-resi', 'as' => 'match-resi.'], function() {
    Route::get('/', [MatchResiController::class, 'index'])->name('index');
    Route::get('/upload-resi', [MatchResiController::class, 'uploadResi'])->name('uploadResi');
    Route::post('/process-data', [MatchResiController::class, 'processData'])->name('processData');
    Route::get('/{id}', [MatchResiController::class, 'show'])->name('show');
    Route::delete('/{id}', [MatchResiController::class, 'destroy'])->name('destroy');
    Route::post('/dataTable', [MatchResiController::class, 'dataTable'])->name('dataTable');
});

Auth::routes();

Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

Route::get('/amil-email-data', function() {
    return view('email-data');
});

//reset pass
Route::patch('/reset/{id}', [App\Http\Controllers\UserController::class, 'reset'])->name('reset');
